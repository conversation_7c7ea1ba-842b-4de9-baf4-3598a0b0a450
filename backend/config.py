"""
应用配置管理模块
"""
import os
from typing import List, Optional, Union
from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings
from pathlib import Path


class Settings(BaseSettings):
    """应用配置类"""

    # Pydantic v2 配置
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        env_prefix="TESTCASE_"
    )

    # API配置
    qwen_api_key: str = Field(..., description="Qwen API密钥")
    deepseek_api_key: str = Field(..., description="DeepSeek API密钥")
    qwen_base_url: str = Field(
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        description="Qwen API基础URL"
    )
    deepseek_base_url: str = Field(
        default="https://api.deepseek.com/v1",
        description="DeepSeek API基础URL"
    )
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    debug: bool = Field(default=False, description="调试模式")
    
    # 安全配置
    allowed_origins: Union[List[str], str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="允许的CORS来源"
    )
    max_file_size: int = Field(
        default=50 * 1024 * 1024,  # 50MB
        description="最大文件大小（字节）"
    )
    allowed_file_extensions: Union[List[str], str] = Field(
        default=[".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".pdf", ".json", ".yaml", ".yml"],
        description="允许的文件扩展名"
    )
    
    # 文件存储配置
    upload_dir: str = Field(default="uploads", description="上传文件目录")
    results_dir: str = Field(default="results", description="结果文件目录")
    temp_dir: str = Field(default="temp", description="临时文件目录")
    file_cleanup_days: int = Field(default=7, description="文件清理天数")
    
    # AI服务配置
    ai_request_timeout: int = Field(default=300, description="AI请求超时时间（秒）")
    max_concurrent_requests: int = Field(default=10, description="最大并发请求数")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")
    log_max_size: int = Field(default=10 * 1024 * 1024, description="日志文件最大大小")
    log_backup_count: int = Field(default=5, description="日志文件备份数量")
    
    @field_validator('allowed_origins')
    @classmethod
    def validate_origins(cls, v):
        """验证CORS来源配置"""
        if isinstance(v, str):
            v = [origin.strip() for origin in v.split(',') if origin.strip()]
        if "*" in v and len(v) > 1:
            raise ValueError("如果使用通配符'*'，不能指定其他来源")
        return v
    
    @field_validator('max_file_size')
    @classmethod
    def validate_file_size(cls, v):
        """验证文件大小限制"""
        if v <= 0:
            raise ValueError("文件大小限制必须大于0")
        if v > 100 * 1024 * 1024:  # 100MB
            raise ValueError("文件大小限制不能超过100MB")
        return v
    
    @field_validator('allowed_file_extensions')
    @classmethod
    def validate_extensions(cls, v):
        """验证文件扩展名"""
        if isinstance(v, str):
            v = [ext.strip() for ext in v.split(',') if ext.strip()]
        for ext in v:
            if not ext.startswith('.'):
                raise ValueError(f"文件扩展名必须以'.'开头: {ext}")
        return [ext.lower() for ext in v]
    
    @field_validator('upload_dir', 'results_dir', 'temp_dir')
    @classmethod
    def validate_directories(cls, v):
        """验证目录路径"""
        if not v or v.strip() == "":
            raise ValueError("目录路径不能为空")
        return v.strip()
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [self.upload_dir, self.results_dir, self.temp_dir]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    



# 创建全局配置实例
settings = Settings()

# 确保目录存在
settings.create_directories()
